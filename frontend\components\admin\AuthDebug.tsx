/**
 * Admin Authentication Debug Component
 * Shows current authentication status for troubleshooting
 */

import React, { useState, useEffect } from 'react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import tokenManager from '../../utils/tokenManager';

const AuthDebug: React.FC = () => {
  const { admin, isAuthenticated, isLoading } = useAdminAuth();
  const [tokenInfo, setTokenInfo] = useState<any>({});

  useEffect(() => {
    const updateTokenInfo = () => {
      const adminToken = tokenManager.getToken('admin');
      const userToken = tokenManager.getToken('user');
      const anyToken = tokenManager.getAnyToken();
      
      setTokenInfo({
        adminToken: adminToken ? `${adminToken.substring(0, 20)}...` : 'none',
        userToken: userToken ? `${userToken.substring(0, 20)}...` : 'none',
        anyToken: anyToken ? `${anyToken.substring(0, 20)}...` : 'none',
        isAdminAuthenticated: tokenManager.isAuthenticated('admin'),
        isUserAuthenticated: tokenManager.isAuthenticated('user'),
        adminTokenLength: adminToken?.length || 0,
        userTokenLength: userToken?.length || 0
      });
    };

    updateTokenInfo();
    const interval = setInterval(updateTokenInfo, 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg text-xs font-mono max-w-sm z-50">
      <h3 className="text-yellow-400 font-bold mb-2">🔐 Auth Debug</h3>
      
      <div className="space-y-1">
        <div>
          <span className="text-blue-400">Auth Status:</span>
          <span className={isAuthenticated ? "text-green-400" : "text-red-400"}>
            {isAuthenticated ? " ✅ Authenticated" : " ❌ Not Authenticated"}
          </span>
        </div>
        
        <div>
          <span className="text-blue-400">Loading:</span>
          <span className={isLoading ? "text-yellow-400" : "text-green-400"}>
            {isLoading ? " ⏳ Loading..." : " ✅ Loaded"}
          </span>
        </div>
        
        <div>
          <span className="text-blue-400">Admin:</span>
          <span className="text-gray-300">
            {admin ? ` ${admin.username} (${admin.role})` : " None"}
          </span>
        </div>
        
        <div className="border-t border-gray-700 pt-2 mt-2">
          <div>
            <span className="text-blue-400">Admin Token:</span>
            <span className="text-gray-300"> {tokenInfo.adminToken}</span>
          </div>
          
          <div>
            <span className="text-blue-400">User Token:</span>
            <span className="text-gray-300"> {tokenInfo.userToken}</span>
          </div>
          
          <div>
            <span className="text-blue-400">Any Token:</span>
            <span className="text-gray-300"> {tokenInfo.anyToken}</span>
          </div>
        </div>
        
        <div className="border-t border-gray-700 pt-2 mt-2">
          <div>
            <span className="text-blue-400">Admin Auth:</span>
            <span className={tokenInfo.isAdminAuthenticated ? "text-green-400" : "text-red-400"}>
              {tokenInfo.isAdminAuthenticated ? " ✅" : " ❌"}
            </span>
          </div>
          
          <div>
            <span className="text-blue-400">User Auth:</span>
            <span className={tokenInfo.isUserAuthenticated ? "text-green-400" : "text-red-400"}>
              {tokenInfo.isUserAuthenticated ? " ✅" : " ❌"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
