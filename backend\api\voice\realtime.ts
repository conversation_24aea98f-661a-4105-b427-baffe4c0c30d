/**
 * Real-time Voice Call API with WORLD Vocoder Integration
 * Handles voice call initiation, management, and real-time audio processing
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { realTimeVoiceStreamingService } from '../../services/realTimeVoiceStreaming';
import { websocketService } from '../../services/websocket';
import { worldVocoderService, WORLD_VOICE_PROFILES } from '../../services/worldVocoderService';
import UserModel from '../../models/User';
import VoiceCallModel from '../../models/VoiceCall';
import AuditLogModel from '../../models/AuditLog';
import * as crypto from 'crypto';
import multer from 'multer';

/**
 * Create a proper WAV file buffer with header
 */
function createWavBuffer(pcmData: Buffer, sampleRate: number, channels: number): Buffer {
  const bitsPerSample = 16;
  const byteRate = sampleRate * channels * bitsPerSample / 8;
  const blockAlign = channels * bitsPerSample / 8;
  const dataSize = pcmData.length;
  const fileSize = 36 + dataSize;

  const wavBuffer = Buffer.alloc(44 + dataSize);
  let offset = 0;

  // RIFF header
  wavBuffer.write('RIFF', offset); offset += 4;
  wavBuffer.writeUInt32LE(fileSize, offset); offset += 4;
  wavBuffer.write('WAVE', offset); offset += 4;

  // fmt chunk
  wavBuffer.write('fmt ', offset); offset += 4;
  wavBuffer.writeUInt32LE(16, offset); offset += 4; // chunk size
  wavBuffer.writeUInt16LE(1, offset); offset += 2; // audio format (PCM)
  wavBuffer.writeUInt16LE(channels, offset); offset += 2;
  wavBuffer.writeUInt32LE(sampleRate, offset); offset += 4;
  wavBuffer.writeUInt32LE(byteRate, offset); offset += 4;
  wavBuffer.writeUInt16LE(blockAlign, offset); offset += 2;
  wavBuffer.writeUInt16LE(bitsPerSample, offset); offset += 2;

  // data chunk
  wavBuffer.write('data', offset); offset += 4;
  wavBuffer.writeUInt32LE(dataSize, offset); offset += 4;

  // Copy PCM data
  pcmData.copy(wavBuffer, offset);

  return wavBuffer;
}

const router = Router();

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed') as any, false);
    }
  }
});

// Helper function to get authenticated user ID (for user-specific operations)
function getAuthenticatedUserId(req: Request, res: Response): string | null {
  if (!(req as any).user || !(req as any).user.id) {
    res.status(401).json({
      success: false,
      error: 'User authentication required'
    });
    return null;
  }
  return (req as any).user.id;
}

// Helper function to get authenticated user or admin ID
function getAuthenticatedId(req: Request, res: Response): { id: string, isAdmin: boolean } | null {
  // Check for admin authentication first
  if ((req as any).admin && (req as any).admin.id) {
    return { id: (req as any).admin.id, isAdmin: true };
  }

  // Check for user authentication
  if ((req as any).user && (req as any).user.id) {
    return { id: (req as any).user.id, isAdmin: false };
  }

  res.status(401).json({
    success: false,
    error: 'Authentication required'
  });
  return null;
}

/**
 * Initiate a voice call with real-time voice morphing
 */
router.post('/initiate', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { recipientId, initiatorProfile, recipientProfile } = req.body;
    const initiatorId = getAuthenticatedUserId(req, res);
    if (!initiatorId) return; // Response already sent by helper

    // Validate users exist
    const [initiator, recipient] = await Promise.all([
      UserModel.findById(initiatorId),
      UserModel.findById(recipientId)
    ]);

    if (!initiator || !recipient) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Validate voice profiles
    const availableProfiles = Object.keys(WORLD_VOICE_PROFILES);
    if (!availableProfiles.includes(initiatorProfile) || !availableProfiles.includes(recipientProfile)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid voice profile'
      });
    }

    // Check profile restrictions (NORMAL_VOICE only for regular users)
    if (initiatorProfile === 'NORMAL_VOICE' && initiator.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    if (recipientProfile === 'NORMAL_VOICE' && recipient.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    // Initiate the call
    const callId = await realTimeVoiceStreamingService.initiateCall(
      initiatorId,
      recipientId,
      initiatorProfile,
      recipientProfile
    );

    // Create call record in database
    const voiceCall = new VoiceCallModel({
      callId,
      initiatorId,
      recipientId,
      status: 'initiating',
      startTime: new Date(),
      voiceProfiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      recordingEnabled: true,
      metadata: {
        worldVocoderEnabled: worldVocoderService.isAvailable(),
        realTimeProcessing: true,
        securityLevel: 'high'
      }
    });

    await voiceCall.save();

    // Send invitation to recipient via WebSocket
    websocketService.sendVoiceCallInvitation(callId, initiatorId, recipientId, {
      initiatorName: initiator.profile?.displayName || initiator.username,
      voiceProfile: recipientProfile
    });

    // Log the call initiation
    await AuditLogModel.create({
      action: 'voice_call_initiated',
      userId: initiatorId,
      targetUserId: recipientId,
      details: {
        callId,
        initiatorProfile,
        recipientProfile,
        worldVocoderEnabled: worldVocoderService.isAvailable()
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'initiating',
      profiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call initiation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate voice call'
    });
  }
});

/**
 * Accept an incoming voice call
 */
router.post('/accept/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is the recipient
    if (voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to accept this call'
      });
    }

    // Update call status
    voiceCall.status = 'ringing';
    await voiceCall.save();

    // Notify initiator that call was accepted
    if (voiceCall.initiatorId) {
      websocketService.broadcastVoiceCallEvent(callId, [voiceCall.initiatorId], {
        action: 'call_accepted',
        acceptedBy: userId
      });
    }

    res.json({
      success: true,
      callId,
      status: 'ringing',
      message: 'Call accepted, establishing connection...'
    });

  } catch (error) {
    console.error('Voice call accept error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to accept voice call'
    });
  }
});

/**
 * End a voice call
 */
router.post('/end/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to end this call'
      });
    }

    // End the call in the streaming service
    await realTimeVoiceStreamingService.endCall(callId, userId);

    // Update call record
    voiceCall.status = 'ended';
    voiceCall.endTime = new Date();
    voiceCall.endedBy = userId;
    await voiceCall.save();

    // Notify other participant
    const otherParticipant = userId === voiceCall.initiatorId ? voiceCall.recipientId : voiceCall.initiatorId;
    if (otherParticipant) {
      websocketService.broadcastVoiceCallEvent(callId, [otherParticipant], {
        action: 'call_ended',
        endedBy: userId
      });
    }

    // Log the call end
    await AuditLogModel.create({
      action: 'voice_call_ended',
      userId,
      targetUserId: otherParticipant,
      details: {
        callId,
        duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime(),
        endedBy: userId
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'ended',
      duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime()
    });

  } catch (error) {
    console.error('Voice call end error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end voice call'
    });
  }
});

/**
 * Get call status and statistics
 */
router.get('/status/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to view this call'
      });
    }

    // Get real-time statistics
    const stats = realTimeVoiceStreamingService.getCallStats(callId);

    res.json({
      success: true,
      callId,
      status: voiceCall.status,
      startTime: voiceCall.startTime,
      endTime: voiceCall.endTime,
      participants: {
        initiator: voiceCall.initiatorId,
        recipient: voiceCall.recipientId
      },
      voiceProfiles: voiceCall.voiceProfiles,
      recordingEnabled: voiceCall.metadata?.recordingEnabled ?? true,
      realTimeStats: stats,
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call status'
    });
  }
});

/**
 * Get available voice profiles for user or admin
 */
router.get('/profiles', authenticateToken, async (req: Request, res: Response) => {
  try {
    // Check if user or admin is authenticated
    const authInfo = getAuthenticatedId(req, res);
    if (!authInfo) return; // Response already sent by helper

    if (authInfo.isAdmin) {
      // Admin access - return all profiles
      const allProfiles = Object.keys(WORLD_VOICE_PROFILES);

      const profileDetails = allProfiles.map(profileName => ({
        name: profileName,
        description: getProfileDescription(profileName),
        parameters: WORLD_VOICE_PROFILES[profileName],
        userType: profileName === 'NORMAL_VOICE' ? 'regular_only' : 'all'
      }));

      res.json({
        success: true,
        profiles: profileDetails,
        worldVocoderEnabled: worldVocoderService.isAvailable(),
        userType: 'admin'
      });
    } else {
      // User access - filter based on user type
      const user = await UserModel.findById(authInfo.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Get all available profiles
      const allProfiles = Object.keys(WORLD_VOICE_PROFILES);

      // Filter profiles based on user type
      const availableProfiles = allProfiles.filter(profileName => {
        // NORMAL_VOICE is only available for regular users
        if (profileName === 'NORMAL_VOICE') {
          return !user.isSuperuser;
        }
        return true;
      });

      // Get profile details
      const profileDetails = availableProfiles.map(profileName => ({
        name: profileName,
        description: getProfileDescription(profileName),
        parameters: WORLD_VOICE_PROFILES[profileName],
        userType: profileName === 'NORMAL_VOICE' ? 'regular_only' : 'all'
      }));

      res.json({
        success: true,
        profiles: profileDetails,
        worldVocoderEnabled: worldVocoderService.isAvailable(),
        userType: user.isSuperuser ? 'superuser' : 'regular'
      });
    }

  } catch (error) {
    console.error('Get voice profiles error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice profiles'
    });
  }
});

/**
 * Get profile description for display
 */
function getProfileDescription(profileName: string): string {
  const descriptions: Record<string, string> = {
    'SECURE_DEEP_MALE': 'Deep, masculine voice with security distortion',
    'SECURE_HIGH_FEMALE': 'Higher pitch, feminine voice with light distortion',
    'ROBOTIC_SYNTHETIC': 'Mechanical, robotic voice effect',
    'WHISPER_SOFT': 'Soft, whispered voice tone',
    'DRAMATIC_BASS': 'Very deep, dramatic bass voice',
    'ETHEREAL_HIGH': 'High-pitched, ethereal voice',
    'MECHANICAL_DRONE': 'Mechanical drone-like voice',
    'WARM_TENOR': 'Warm, tenor voice quality',
    'CRYSTAL_SOPRANO': 'Clear, high soprano voice',
    'DARK_BARITONE': 'Dark, rich baritone voice',
    'BRIGHT_ALTO': 'Bright, alto voice range',
    'MYSTERIOUS_ECHO': 'Mysterious voice with echo effects',
    'ENERGETIC_YOUNG': 'Energetic, youthful voice',
    'WISE_ELDER': 'Mature, wise voice tone',
    'DIGITAL_GLITCH': 'Digital, glitched voice effect',
    'SMOOTH_RADIO': 'Smooth, radio announcer voice',
    'INTENSE_GROWL': 'Intense, growling voice',
    'GENTLE_BREEZE': 'Gentle, soft voice like a breeze',
    'POWERFUL_BOOM': 'Powerful, booming voice',
    'SUBTLE_SHIFT': 'Subtle voice modification',
    'NORMAL_VOICE': 'Natural voice (regular users only)'
  };

  return descriptions[profileName] || 'Custom voice profile';
}

/**
 * Test a voice profile with uploaded audio
 * POST /api/voice/realtime/test-profile
 */
router.post('/test-profile', authenticateToken, upload.single('audio'), async (req: Request, res: Response) => {
  try {
    const authInfo = getAuthenticatedId(req, res);
    if (!authInfo) return; // Response already sent by helper

    const { profileName } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
    }

    if (!profileName || !WORLD_VOICE_PROFILES[profileName]) {
      return res.status(400).json({
        success: false,
        error: 'Valid profile name is required',
        availableProfiles: Object.keys(WORLD_VOICE_PROFILES)
      });
    }

    // Check if WORLD vocoder is available
    if (!worldVocoderService.isAvailable()) {
      return res.status(503).json({
        success: false,
        error: 'WORLD vocoder service is not available'
      });
    }

    console.log('Voice profile test request:', {
      profileName,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      isAdmin: authInfo.isAdmin
    });

    // Process the audio with the specified profile
    const profile = WORLD_VOICE_PROFILES[profileName];

    // Create a temporary session for processing
    const sessionId = await worldVocoderService.createSession(authInfo.id);

    try {
      console.log('🔄 Starting audio buffer conversion...');

      // Simple conversion assuming the WAV file data (skip header if present)
      let audioData: Float32Array;

      try {
        // Validate file size
        if (req.file.buffer.length < 100) {
          throw new Error('Audio file too small');
        }

        if (req.file.buffer.length > 50 * 1024 * 1024) { // 50MB limit
          throw new Error('Audio file too large (max 50MB)');
        }

        // Simple WAV header detection and parsing
        let bufferStart = 0;
        let sampleRate = 48000; // Default
        let channels = 1;
        let bitsPerSample = 16;

        // Check for WAV header
        const header = req.file.buffer.toString('ascii', 0, 4);
        if (header === 'RIFF') {
          // Parse basic WAV header
          const fmtChunk = req.file.buffer.toString('ascii', 8, 12);
          if (fmtChunk === 'WAVE') {
            // Look for fmt chunk
            let offset = 12;
            while (offset < req.file.buffer.length - 8) {
              const chunkId = req.file.buffer.toString('ascii', offset, offset + 4);
              const chunkSize = req.file.buffer.readUInt32LE(offset + 4);

              if (chunkId === 'fmt ') {
                // Read format data
                channels = req.file.buffer.readUInt16LE(offset + 10);
                sampleRate = req.file.buffer.readUInt32LE(offset + 12);
                bitsPerSample = req.file.buffer.readUInt16LE(offset + 22);
                break;
              } else if (chunkId === 'data') {
                bufferStart = offset + 8;
                break;
              }

              offset += 8 + chunkSize;
            }

            if (bufferStart === 0) {
              // Fallback to standard header size
              bufferStart = 44;
            }
          }
        }

        const audioBuffer = req.file.buffer.slice(bufferStart);

        // Validate audio buffer
        if (audioBuffer.length < 2) {
          throw new Error('No audio data found after header');
        }

        // Convert to Float32Array based on bit depth
        if (bitsPerSample === 16) {
          audioData = new Float32Array(audioBuffer.length / 2);
          const dataView = new DataView(audioBuffer.buffer, audioBuffer.byteOffset);

          for (let i = 0; i < audioData.length; i++) {
            audioData[i] = dataView.getInt16(i * 2, true) / 32768.0;
          }
        } else if (bitsPerSample === 32) {
          audioData = new Float32Array(audioBuffer.length / 4);
          const dataView = new DataView(audioBuffer.buffer, audioBuffer.byteOffset);

          for (let i = 0; i < audioData.length; i++) {
            audioData[i] = dataView.getFloat32(i * 4, true);
          }
        } else {
          throw new Error(`Unsupported bit depth: ${bitsPerSample}`);
        }

        // Convert stereo to mono if needed
        if (channels === 2) {
          const monoData = new Float32Array(audioData.length / 2);
          for (let i = 0; i < monoData.length; i++) {
            monoData[i] = (audioData[i * 2] + audioData[i * 2 + 1]) / 2;
          }
          audioData = monoData;
        }

        console.log('✅ Audio conversion completed:', {
          originalSize: req.file.buffer.length,
          audioSamples: audioData.length,
          sampleRate,
          channels,
          bitsPerSample,
          duration: `${(audioData.length / sampleRate).toFixed(2)}s`
        });
      } catch (conversionError) {
        console.error('❌ Audio conversion failed:', conversionError);
        const errorMessage = conversionError instanceof Error ? conversionError.message : String(conversionError);
        throw new Error(`Failed to convert audio data: ${errorMessage}`);
      }

      console.log('🔄 Processing audio with WORLD vocoder...');

      // Process audio in chunks for large files
      const CHUNK_SIZE = 960; // 20ms at 48kHz
      const chunks: Float32Array[] = [];

      for (let i = 0; i < audioData.length; i += CHUNK_SIZE) {
        const chunk = audioData.slice(i, Math.min(i + CHUNK_SIZE, audioData.length));
        chunks.push(chunk);
      }

      console.log(`🔄 Processing ${chunks.length} audio chunks...`);

      // Process each chunk with timeout and retry logic
      const processedChunks: Float32Array[] = [];
      let successfulChunks = 0;
      let failedChunks = 0;

      for (let i = 0; i < chunks.length; i++) {
        try {
          console.log(`🔄 Processing chunk ${i + 1}/${chunks.length} (${chunks[i].length} samples)...`);

          // Add timeout for each chunk
          const chunkPromise = worldVocoderService.processAudioFrame(
            sessionId,
            chunks[i],
            profile
          );

          const timeoutPromise = new Promise<Float32Array | null>((_, reject) => {
            setTimeout(() => reject(new Error('Chunk processing timeout')), 5000);
          });

          const processedChunk = await Promise.race([chunkPromise, timeoutPromise]);

          if (processedChunk && processedChunk.length > 0) {
            processedChunks.push(processedChunk);
            successfulChunks++;
          } else {
            console.warn(`⚠️ Chunk ${i + 1} processing returned null/empty, using original chunk`);
            processedChunks.push(chunks[i]);
            failedChunks++;
          }
        } catch (chunkError) {
          console.error(`❌ Error processing chunk ${i + 1}:`, chunkError instanceof Error ? chunkError.message : String(chunkError));
          // Use original chunk as fallback
          processedChunks.push(chunks[i]);
          failedChunks++;
        }

        // Stop processing if too many chunks fail
        if (failedChunks > chunks.length * 0.5) {
          console.error(`❌ Too many chunk failures (${failedChunks}/${chunks.length}), stopping processing`);
          throw new Error('Voice processing failed: too many chunk failures');
        }
      }

      console.log(`✅ Chunk processing completed: ${successfulChunks} successful, ${failedChunks} failed`);

      if (processedChunks.length === 0) {
        throw new Error('No chunks were processed successfully');
      }

      // Combine processed chunks
      const totalLength = processedChunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const processedAudio = new Float32Array(totalLength);
      let offset = 0;
      for (const chunk of processedChunks) {
        processedAudio.set(chunk, offset);
        offset += chunk.length;
      }

      if (!processedAudio) {
        throw new Error('Audio processing returned null');
      }

      console.log('✅ Audio processing completed:', {
        inputSamples: audioData.length,
        outputSamples: processedAudio.length
      });

      // Convert back to buffer (16-bit PCM)
      const pcmBuffer = Buffer.alloc(processedAudio.length * 2);
      const pcmView = new DataView(pcmBuffer.buffer);

      for (let i = 0; i < processedAudio.length; i++) {
        const sample = Math.max(-1, Math.min(1, processedAudio[i]));
        pcmView.setInt16(i * 2, sample * 32767, true);
      }

      // Create proper WAV file with header
      const wavBuffer = createWavBuffer(pcmBuffer, 44100, 1);

      console.log('✅ Audio response prepared, sending to client...', {
        pcmSize: pcmBuffer.length,
        wavSize: wavBuffer.length,
        sampleRate: 44100,
        channels: 1
      });

      // Set appropriate headers for audio response
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Length': wavBuffer.length.toString(),
        'Cache-Control': 'no-cache'
      });

      // Send the processed audio
      res.send(wavBuffer);

      // Log the test activity for auditing (only if successful)
      try {
        await AuditLogModel.create({
          logId: `voice_test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          adminId: authInfo.isAdmin ? authInfo.id : undefined,
          userId: !authInfo.isAdmin ? authInfo.id : undefined,

          event: {
            type: 'admin_action',
            action: 'voice_profile_test',
            resource: `/api/voice/realtime/test-profile`,
            result: 'success',
            severity: 'low'
          },

          context: {
            ipAddress: req.ip || req.socket.remoteAddress || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            endpoint: req.originalUrl,
            method: req.method,
            statusCode: 200
          },

          security: {
            riskScore: 10,
            authMethod: 'both',
            mfaUsed: false,
            suspiciousActivity: false
          },

          data: {
            metadata: {
              profileName,
              fileSize: req.file.size,
              mimeType: req.file.mimetype,
              outputSize: wavBuffer.length
            }
          },

          compliance: {
            category: 'system_admin',
            retention: 'medium',
            piiIncluded: false,
            sensitiveData: false,
            exportable: true
          },

          createdAt: new Date()
        });
        console.log('✅ Audit log created successfully');
      } catch (auditError) {
        console.warn('⚠️ Failed to create audit log:', auditError);
        // Don't fail the request if audit logging fails
      }

    } catch (processingError) {
      console.error('❌ Audio processing failed:', processingError);
      throw processingError;
    } finally {
      // Clean up the session
      try {
        await worldVocoderService.destroySession(sessionId);
        console.log('✅ Session cleanup completed');
      } catch (cleanupError) {
        console.warn('⚠️ Session cleanup failed:', cleanupError);
      }
    }

  } catch (error) {
    console.error('Voice profile test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process audio with voice profile'
    });
  }
});

export default router;
