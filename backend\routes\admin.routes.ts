import { Router } from 'express';
import * as AdminUserController from '../controllers/admin/user.controller';
import * as AdminController from '../controllers/admin/admin.controller';
import * as UserDetailedController from '../controllers/admin/user-detailed.controller';
import * as UserController from '../controllers/user.controller';
import UserModel from '../models/User';
import { auth, requireAdmin } from '../middleware/auth';
import validate from '../middleware/validate';
import Joi from 'joi';
import { dashboardStatsHandler } from '../api/admin/dashboard';
import adminChatRouter from '../api/admin/chat';

// Validation schemas
const createAdminSchema = Joi.object({
  username: Joi.string().required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  role: Joi.string().valid('admin', 'superadmin'),
  ppkEnabled: Joi.boolean(),
  authMethod: Joi.string().valid('password', 'ppk', 'both')
});

const updateAdminSchema = Joi.object({
  email: Joi.string().email(),
  role: Joi.string().valid('admin', 'superadmin'),
  isActive: Joi.boolean(),
  authMethod: Joi.string().valid('password', 'ppk', 'both')
});

const verifyPPKSchema = Joi.object({
  challenge: Joi.string().required(),
  signature: Joi.string().required()
});

const createUserSchema = Joi.object({
  username: Joi.string().required().trim(),
  expression: Joi.string().required(),
  displayName: Joi.string().required().trim(),
  isSuperuser: Joi.boolean().optional()
});

const updateUserSchema = Joi.object({
  displayName: Joi.string().optional().trim(),
  expression: Joi.string().optional(),
  isSuperuser: Joi.boolean().optional(),
  status: Joi.string().valid('active', 'inactive', 'locked', 'pending_device_registration').optional()
});

const updateUserStatusSchema = Joi.object({
  status: Joi.string().valid('active', 'inactive', 'locked', 'pending_device_registration').required()
});

const router = Router();

// User management routes
router.get('/users', auth, requireAdmin, AdminUserController.getUsers);
router.get('/users/detailed', auth, requireAdmin, UserDetailedController.getUsersDetailed);
router.post('/users', auth, requireAdmin, validate(createUserSchema), AdminUserController.createUser);
router.get('/users/:id', auth, requireAdmin, AdminUserController.getUserById);
router.get('/users/:id/detailed', auth, requireAdmin, UserDetailedController.getUserDetailed);
router.put('/users/:id', auth, requireAdmin, validate(updateUserSchema), AdminUserController.updateUser);
router.delete('/users/:id', auth, requireAdmin, AdminUserController.deleteUser);
router.patch('/users/:id/status', auth, requireAdmin, validate(updateUserStatusSchema), AdminUserController.updateUserStatus);
router.post('/users/:id/reset-device', auth, requireAdmin, AdminUserController.resetUserDevice);
router.get('/users/:id/activity', auth, requireAdmin, AdminUserController.getUserActivity);
router.post('/users/:id/add-ble-device', auth, requireAdmin, AdminUserController.addBleDevice);

// New admin action routes for device and session management
router.post('/users/:id/reset-devices', auth, requireAdmin, UserController.resetUserDevicesController);
router.post('/users/:id/reset-ble', auth, requireAdmin, UserController.resetUserBLEController);
router.post('/users/:id/reset-sessions', auth, requireAdmin, UserController.resetUserSessionsController);
router.post('/users/:id/unlock', auth, requireAdmin, UserController.unlockUserController);
router.post('/users/:id/reset-expression', auth, requireAdmin, UserController.resetUserExpressionController);

// Voice profile management
router.put('/users/:id/voice-profile', auth, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { profileName } = req.body;
    
    if (!profileName) {
      return res.status(400).json({
        success: false,
        error: 'Profile name is required'
      });
    }
    
    // Update user's voice profile
    const user = await UserModel.findByIdAndUpdate(
      id, 
      { 'voiceSettings.worldProfile': profileName },
      { new: true }
    );
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    res.json({
      success: true,
      message: `Voice profile updated to ${profileName}`,
      user: {
        id: user._id,
        username: user.username,
        voiceProfile: user.voiceSettings?.worldProfile || profileName
      }
    });
  } catch (error) {
    console.error('Voice profile update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voice profile'
    });
  }
});

// Admin management routes
router.get('/admins', auth, requireAdmin, AdminController.getAdmins);
router.post('/admins', auth, requireAdmin, validate(createAdminSchema), AdminController.createAdmin);
router.put('/admins/:id', auth, requireAdmin, validate(updateAdminSchema), AdminController.updateAdmin);
router.delete('/admins/:id', auth, requireAdmin, AdminController.deleteAdmin);

// PPK key management routes
router.post('/admins/:id/generate-ppk', auth, requireAdmin, AdminController.generateAdminPPK);
router.post('/admins/:id/verify-ppk', auth, requireAdmin, validate(verifyPPKSchema), AdminController.verifyAdminPPK);

// Dashboard stats route
router.get('/dashboard/stats', auth, requireAdmin, dashboardStatsHandler);

// Chat monitoring routes
router.use('/chat', adminChatRouter);

// User chat messages route
router.get('/users/:id/chat-messages', auth, requireAdmin, UserDetailedController.getUserChatMessages);

// User voice recordings routes
router.get('/users/:id/voice-recordings', auth, requireAdmin, UserDetailedController.getUserVoiceRecordings);
router.get('/voice-recordings/:recordingId/play', auth, requireAdmin, UserDetailedController.playVoiceRecording);
router.get('/voice-recordings/:recordingId/download', auth, requireAdmin, UserDetailedController.downloadVoiceRecording);
router.delete('/voice-recordings/:recordingId', auth, requireAdmin, UserDetailedController.deleteVoiceRecording);

// User device details route
router.get('/users/:id/device-details', auth, requireAdmin, UserDetailedController.getUserDeviceDetails);

// User voice settings route
router.get('/users/:id/voice-settings', auth, requireAdmin, UserDetailedController.getUserVoiceSettings);

export default router;
