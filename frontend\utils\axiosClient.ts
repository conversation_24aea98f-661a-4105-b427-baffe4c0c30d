import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import tokenManager from './tokenManager';
import { csrfManager } from './csrfManager';
import { isBrowser } from './browserUtils';

// Create axios instances for different endpoints
export const frontendApi = axios.create({
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

export const backendApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Setup CSRF protection for both instances (only in browser)
if (isBrowser()) {
  csrfManager.setupInterceptors(frontendApi);
  csrfManager.setupInterceptors(backendApi);
}

// Request interceptor to add auth token
const addAuthInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    (config) => {
      // For admin endpoints, prioritize admin token
      const isAdminEndpoint = config.url?.includes('/admin/') || 
                             config.url?.includes('profiles-enhanced') ||
                             config.url?.includes('/voice/');
      let token: string | null = null;
      
      if (isAdminEndpoint) {
        token = tokenManager.getToken('admin');
        console.log('🔐 Admin endpoint detected:', config.url, 'token:', token ? 'present' : 'missing');
        
        // Debug: Log all available tokens
        const adminToken = tokenManager.getToken('admin');
        const userToken = tokenManager.getToken('user');
        const anyToken = tokenManager.getAnyToken();
        console.log('🔐 Token debug:', {
          adminToken: adminToken ? `${adminToken.substring(0, 10)}...` : 'none',
          userToken: userToken ? `${userToken.substring(0, 10)}...` : 'none',
          anyToken: anyToken ? `${anyToken.substring(0, 10)}...` : 'none',
          isAdminAuthenticated: tokenManager.isAuthenticated('admin'),
          isUserAuthenticated: tokenManager.isAuthenticated('user')
        });
      } else {
        token = tokenManager.getAnyToken();
        console.log('🔐 General endpoint:', config.url, 'token:', token ? 'present' : 'missing');
      }
      
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('🔐 Added Authorization header to request:', config.url);
      } else {
        console.warn('🔐 No token available for request:', config.url);
        
        // If it's an admin endpoint and we don't have a token, this is a problem
        if (isAdminEndpoint) {
          console.error('🔐 CRITICAL: Admin endpoint accessed without admin token!');
        }
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

// Response interceptor for error handling
const addResponseInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      if (error.response?.status === 401) {
        // Only clear tokens and redirect for certain endpoints
        const isAuthEndpoint = error.config?.url?.includes('/auth/') || 
                              error.config?.url?.includes('/session');
        
        if (isAuthEndpoint) {
          console.warn('🔐 Authentication failed for auth endpoint, clearing tokens');
          tokenManager.clearAllTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/?session=expired';
          }
        } else {
          console.warn('🔐 401 error for non-auth endpoint:', error.config?.url);
        }
      }
      return Promise.reject(error);
    }
  );
};

// Apply interceptors
addAuthInterceptor(frontendApi);
addAuthInterceptor(backendApi);
addResponseInterceptor(frontendApi);
addResponseInterceptor(backendApi);

// Utility functions for common API calls
export const apiClient = {
  // Frontend API calls
  frontend: {
    get: (url: string, config?: AxiosRequestConfig) => frontendApi.get(url, config),
    post: (url: string, data?: any, config?: AxiosRequestConfig) => frontendApi.post(url, data, config),
    put: (url: string, data?: any, config?: AxiosRequestConfig) => frontendApi.put(url, data, config),
    delete: (url: string, config?: AxiosRequestConfig) => frontendApi.delete(url, config),
  },
  
  // Backend API calls
  backend: {
    get: (url: string, config?: AxiosRequestConfig) => backendApi.get(url, config),
    post: (url: string, data?: any, config?: AxiosRequestConfig) => backendApi.post(url, data, config),
    put: (url: string, data?: any, config?: AxiosRequestConfig) => backendApi.put(url, data, config),
    delete: (url: string, config?: AxiosRequestConfig) => backendApi.delete(url, config),
  },
};

export default apiClient;
