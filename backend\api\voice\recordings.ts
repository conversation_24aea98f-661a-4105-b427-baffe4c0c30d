/**
 * Voice Call Recordings API
 * Handles voice call recording management for admin panel
 * Provides playback, download, and delete functionality
 */

import { Router, Request, Response } from 'express';
import { authenticateToken, requireAdmin } from '../../middleware/auth';
import { voiceCallRecordingService } from '../../services/voiceCallRecording';
import VoiceCallModel from '../../models/VoiceCall';
import AuditLogModel from '../../models/AuditLog';
import * as fs from 'fs';
import * as path from 'path';

const router = Router();

/**
 * Get all voice call recordings (admin only)
 */
router.get('/', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const userId = req.query.userId as string;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;

    // Build query filters
    const filters: any = { recordingPath: { $exists: true } };
    
    if (userId) {
      filters.$or = [
        { callerId: userId },
        { recipientId: userId },
        { initiatorId: userId }
      ];
    }

    if (startDate || endDate) {
      filters.startTime = {};
      if (startDate) filters.startTime.$gte = new Date(startDate);
      if (endDate) filters.startTime.$lte = new Date(endDate);
    }

    const skip = (page - 1) * limit;
    
    const [recordings, total] = await Promise.all([
      VoiceCallModel.find(filters)
        .sort({ startTime: -1 })
        .skip(skip)
        .limit(limit)
        .populate('callerId', 'username profile.displayName')
        .populate('recipientId', 'username profile.displayName')
        .lean(),
      VoiceCallModel.countDocuments(filters)
    ]);

    const recordingsWithMetadata = recordings.map(recording => ({
      callId: recording.callId,
      startTime: recording.startTime,
      endTime: recording.endTime,
      duration: recording.recordingDuration || 0,
      fileSize: recording.recordingSize || 0,
      participants: {
        initiator: {
          id: recording.initiatorId || recording.callerId,
          name: (recording as any).callerId?.profile?.displayName || 
                (recording as any).callerId?.username || 'Unknown'
        },
        recipient: {
          id: recording.recipientId,
          name: (recording as any).recipientId?.profile?.displayName || 
                (recording as any).recipientId?.username || 'Unknown'
        }
      },
      voiceProfiles: recording.voiceProfiles || {
        initiator: recording.morphingProfile || 'SECURE_DEEP_MALE',
        recipient: 'SECURE_HIGH_FEMALE'
      },
      status: recording.status,
      worldVocoderEnabled: recording.metadata?.worldVocoderEnabled || false,
      hasRecording: !!recording.recordingPath
    }));

    res.json({
      success: true,
      recordings: recordingsWithMetadata,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get recordings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice call recordings'
    });
  }
});

/**
 * Get specific recording metadata
 */
router.get('/:callId', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;

    const recording = await voiceCallRecordingService.getRecording(callId);
    if (!recording) {
      return res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
    }

    // Get file stats
    const stats = fs.statSync(recording.filePath);
    
    res.json({
      success: true,
      recording: {
        callId: recording.metadata.callId,
        filePath: recording.filePath,
        fileSize: stats.size,
        duration: recording.metadata.duration,
        startTime: recording.metadata.startTime,
        endTime: recording.metadata.endTime,
        participants: recording.metadata.participants,
        audioMetadata: recording.metadata.metadata,
        encrypted: !!recording.decryptionKey
      }
    });

  } catch (error) {
    console.error('Get recording metadata error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get recording metadata'
    });
  }
});

/**
 * Stream recording for playback
 */
router.get('/:callId/stream', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const adminUserId = (req as any).user.id;

    const recording = await voiceCallRecordingService.getRecording(callId);
    if (!recording) {
      return res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
    }

    // Check if file exists
    if (!fs.existsSync(recording.filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
    }

    // Log access
    await AuditLogModel.create({
      action: 'voice_recording_accessed',
      userId: adminUserId,
      details: {
        callId,
        recordingPath: recording.filePath,
        accessType: 'stream'
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    // Set appropriate headers
    const stats = fs.statSync(recording.filePath);
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Accept-Ranges', 'bytes');
    res.setHeader('Content-Disposition', `inline; filename="${callId}.wav"`);

    // Handle range requests for audio seeking
    const range = req.headers.range;
    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : stats.size - 1;
      const chunksize = (end - start) + 1;

      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${stats.size}`);
      res.setHeader('Content-Length', chunksize);

      const stream = fs.createReadStream(recording.filePath, { start, end });
      stream.pipe(res);
    } else {
      const stream = fs.createReadStream(recording.filePath);
      stream.pipe(res);
    }

  } catch (error) {
    console.error('Stream recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stream recording'
    });
  }
});

/**
 * Download recording file
 */
router.get('/:callId/download', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const adminUserId = (req as any).user.id;

    const recording = await voiceCallRecordingService.getRecording(callId);
    if (!recording) {
      return res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
    }

    // Check if file exists
    if (!fs.existsSync(recording.filePath)) {
      return res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
    }

    // Log download
    await AuditLogModel.create({
      action: 'voice_recording_downloaded',
      userId: adminUserId,
      details: {
        callId,
        recordingPath: recording.filePath,
        accessType: 'download'
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    // Set download headers
    const filename = `voice_call_${callId}_${recording.metadata.startTime.toISOString().split('T')[0]}.wav`;
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Stream file
    const stream = fs.createReadStream(recording.filePath);
    stream.pipe(res);

  } catch (error) {
    console.error('Download recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download recording'
    });
  }
});

/**
 * Delete recording
 */
router.delete('/:callId', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const adminUserId = (req as any).user.id;

    const success = await voiceCallRecordingService.deleteRecording(callId);
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Recording not found or already deleted'
      });
    }

    // Log deletion
    await AuditLogModel.create({
      action: 'voice_recording_deleted',
      userId: adminUserId,
      details: {
        callId,
        deletedBy: adminUserId
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Recording deleted successfully'
    });

  } catch (error) {
    console.error('Delete recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete recording'
    });
  }
});

/**
 * Get recording statistics
 */
router.get('/stats/overview', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const timeRange = req.query.timeRange as string || '30d';
    
    let startDate = new Date();
    switch (timeRange) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    const stats = await VoiceCallModel.aggregate([
      {
        $match: {
          startTime: { $gte: startDate },
          recordingPath: { $exists: true }
        }
      },
      {
        $group: {
          _id: null,
          totalRecordings: { $sum: 1 },
          totalDuration: { $sum: '$recordingDuration' },
          totalSize: { $sum: '$recordingSize' },
          averageDuration: { $avg: '$recordingDuration' },
          worldVocoderUsage: {
            $sum: { $cond: ['$metadata.worldVocoderEnabled', 1, 0] }
          }
        }
      }
    ]);

    const result = stats[0] || {
      totalRecordings: 0,
      totalDuration: 0,
      totalSize: 0,
      averageDuration: 0,
      worldVocoderUsage: 0
    };

    res.json({
      success: true,
      stats: {
        totalRecordings: result.totalRecordings,
        totalDuration: Math.round(result.totalDuration / 1000), // Convert to seconds
        totalSize: result.totalSize,
        averageDuration: Math.round(result.averageDuration / 1000), // Convert to seconds
        worldVocoderUsage: result.worldVocoderUsage,
        worldVocoderPercentage: result.totalRecordings > 0 
          ? Math.round((result.worldVocoderUsage / result.totalRecordings) * 100)
          : 0,
        timeRange
      }
    });

  } catch (error) {
    console.error('Get recording stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get recording statistics'
    });
  }
});

export default router;
