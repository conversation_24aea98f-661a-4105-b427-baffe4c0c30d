/**
 * Enhanced Admin Authentication Context
 * Provides centralized session management for admin panel with modern UI/UX standards
 */

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useRouter } from 'next/router';
import { apiClient } from '../utils/axiosClient';
import { sessionManager } from '../utils/sessionManager';
import tokenManager from '../utils/tokenManager';

interface Admin {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'superadmin';
  isActive: boolean;
  lastLogin: string;
}

interface AdminAuthContextType {
  admin: Admin | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
  checkAuthStatus: () => Promise<void>;
  checkPermission: (requiredRole?: 'admin' | 'superadmin') => boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | null>(null);

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

interface AdminAuthProviderProps {
  children: ReactNode;
}

export const AdminAuthProvider: React.FC<AdminAuthProviderProps> = ({ children }) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  // Check initial authentication status
  useEffect(() => {
    // Add a small delay to prevent racing with page-level auth checks
    const timer = setTimeout(() => {
      checkAuthStatus();
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // Setup session monitoring
  useEffect(() => {
    if (isAuthenticated) {
      const unsubscribe = sessionManager.onSessionExpired(() => {
        handleSessionExpired();
      });
      
      return unsubscribe;
    }
  }, [isAuthenticated]);

  const checkAuthStatus = async () => {
    // Only run in browser
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      console.log('🔐 AdminAuthContext: Checking auth status...');
      
      // Check if we have a stored token using tokenManager
      const hasAdminToken = tokenManager.isAuthenticated('admin');
      const adminToken = tokenManager.getToken('admin');
      
      console.log('🔐 AdminAuthContext: Token status:', {
        hasAdminToken,
        tokenLength: adminToken?.length || 0,
        tokenPreview: adminToken ? `${adminToken.substring(0, 10)}...` : 'none'
      });
      
      if (!hasAdminToken) {
        console.log('🔐 AdminAuthContext: No admin token found');
        setIsLoading(false);
        return;
      }

      console.log('🔐 AdminAuthContext: Token found, verifying with backend...');
      // Verify token with backend
      const response = await apiClient.backend.get('/api/auth/admin/session');
      
      console.log('🔐 AdminAuthContext: Backend response:', {
        status: response.status,
        success: response.data.success,
        hasAdmin: !!response.data.admin
      });
      
      if (response.data.success && response.data.admin) {
        console.log('🔐 AdminAuthContext: Authentication successful');
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
        sessionManager.startSession();
      } else {
        console.log('🔐 AdminAuthContext: Invalid token response');
        // Invalid token, clear storage
        tokenManager.clearAllTokens();
        setIsAuthenticated(false);
        setAdmin(null);
      }
    } catch (error: any) {
      console.error('🔐 AdminAuthContext: Auth check failed:', error);
      console.error('🔐 Error details:', {
        message: error?.message || 'Unknown error',
        status: error?.response?.status || 'No status',
        data: error?.response?.data || 'No data'
      });
      tokenManager.clearAllTokens();
      setIsAuthenticated(false);
      setAdmin(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await apiClient.backend.post('/api/auth/admin/login/complete', {
        username,
        password
      });

      if (response.data.success && response.data.admin && response.data.token) {
        // Store token directly since cookies aren't working cross-origin
        tokenManager.setToken(response.data.token, 'admin');
        
        // Update state
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
        
        // Start session monitoring
        sessionManager.startSession();
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Call logout endpoint
      await apiClient.backend.post('/api/auth/admin/logout');
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear local state regardless of API call result
      tokenManager.clearAllTokens();
      setAdmin(null);
      setIsAuthenticated(false);
      sessionManager.endSession();
      router.push('/admin/login');
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    try {
      const response = await apiClient.backend.post('/api/auth/admin/refresh');
      
      if (response.data.success && response.data.token) {
        // Backend will set new cookies, no manual storage needed
        sessionManager.refreshSession();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Session refresh failed:', error);
      return false;
    }
  };

  const checkPermission = (requiredRole?: 'admin' | 'superadmin'): boolean => {
    if (!admin || !isAuthenticated) return false;
    
    if (!requiredRole) return true;
    
    if (requiredRole === 'admin') {
      return admin.role === 'admin' || admin.role === 'superadmin';
    }
    
    if (requiredRole === 'superadmin') {
      return admin.role === 'superadmin';
    }
    
    return false;
  };

  const handleSessionExpired = () => {
    setAdmin(null);
    setIsAuthenticated(false);
    localStorage.removeItem('admin-token');
    localStorage.removeItem('admin-data');
    router.push('/admin/login?expired=true');
  };

  const value: AdminAuthContextType = {
    admin,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshSession,
    checkAuthStatus,
    checkPermission
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

// Higher-order component for route protection
export interface WithAdminAuthOptions {
  requiredRole?: 'admin' | 'superadmin';
  redirectTo?: string;
}

export function withAdminAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAdminAuthOptions = {}
) {
  const { requiredRole, redirectTo = '/admin/login' } = options;

  const ProtectedComponent: React.FC<P> = (props) => {
    const { admin, isLoading, isAuthenticated, checkPermission } = useAdminAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        if (!isAuthenticated) {
          router.push(redirectTo);
          return;
        }

        if (!checkPermission(requiredRole)) {
          router.push('/admin/unauthorized');
          return;
        }
      }
    }, [isLoading, isAuthenticated, admin, router]);

    if (isLoading) {
      return (
        <div className="admin-loading">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      );
    }

    if (!isAuthenticated || !checkPermission(requiredRole)) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };

  ProtectedComponent.displayName = `withAdminAuth(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return ProtectedComponent;
}
