/**
 * Enhanced Real-Time Voice Neutralization Service - Clarity Focused
 * Removes unique voice characteristics while preserving speech intelligibility
 * Optimized for real-time voice calls with maximum clarity preservation
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { spawn } from 'child_process';

export interface NeutralizationConfig {
    level: 'LIGHT' | 'MEDIUM' | 'HEAVY';
    preserveClarity: boolean;
    realTimeMode: boolean;
    latencyTarget: number; // milliseconds
    processing: {
        f0Neutralization: boolean;
        formantNormalization: boolean;
        spectralSmoothing: number;
        temporalJitter: number;
        noiseLevel: number;
        additionalPasses?: number;
    };
}

export const NEUTRALIZATION_PROFILES = {
    // For real-time voice calls - prioritize clarity and intelligibility
    REAL_TIME_LIGHT: {
        level: 'LIGHT' as const,
        preserveClarity: true,
        realTimeMode: true,
        latencyTarget: 20, // 20ms for real-time calls
        processing: {
            f0Neutralization: true,
            formantNormalization: false, // Skip for maximum clarity
            spectralSmoothing: 0.05, // Very minimal smoothing to preserve clarity
            temporalJitter: 0.5, // ±0.5ms minimal jitter
            noiseLevel: 0.0005 // Extremely low noise level
        }
    },

    REAL_TIME_MEDIUM: {
        level: 'MEDIUM' as const,
        preserveClarity: true,
        realTimeMode: true,
        latencyTarget: 40, // 40ms acceptable for calls
        processing: {
            f0Neutralization: true,
            formantNormalization: false, // Disable to preserve clarity
            spectralSmoothing: 0.08, // Very light smoothing
            temporalJitter: 1, // ±1ms
            noiseLevel: 0.001 // Very light noise
        }
    },

    // For recorded messages (can afford more processing while maintaining clarity)
    OFFLINE_HEAVY: {
        level: 'HEAVY' as const,
        preserveClarity: true,
        realTimeMode: false,
        latencyTarget: 200, // For recorded messages
        processing: {
            f0Neutralization: true,
            formantNormalization: false, // Keep disabled for clarity
            spectralSmoothing: 0.12, // Light smoothing
            temporalJitter: 1.5, // ±1.5ms
            noiseLevel: 0.002, // Light noise
            additionalPasses: 0 // No additional passes for now
        }
    }
};

export class LightweightVoiceNeutralizer {
    private tempDir: string;
    private frameSize: number = 1024;
    private sampleRate: number = 44100;
    private neutralFrequency: number = 150; // Target neutral F0
    private soxAvailable: boolean = false;

    constructor() {
        this.tempDir = path.join(__dirname, '../temp/neutralized');
        this.ensureTempDir();
        this.checkSoxAvailability();
    }

    private async ensureTempDir(): Promise<void> {
        try {
            await fs.mkdir(this.tempDir, { recursive: true });
        } catch (error) {
            console.error('Failed to create neutralization temp directory:', error);
        }
    }

    /**
     * Check if SoX is available for enhanced processing
     */
    private async checkSoxAvailability(): Promise<void> {
        try {
            const sox = spawn('sox', ['--version']);
            sox.on('close', (code) => {
                this.soxAvailable = code === 0;
                console.log('SoX availability for neutralization:', this.soxAvailable);
            });
            sox.on('error', () => {
                this.soxAvailable = false;
                console.warn('⚠️ SoX not available - using clarity-focused fallback processing');
            });
        } catch (error) {
            this.soxAvailable = false;
        }
    }

    /**
     * Main neutralization function that prioritizes audio clarity
     */
    async neutralizeVoice(
        inputBuffer: Buffer,
        config: NeutralizationConfig = NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM,
        userId?: string
    ): Promise<Buffer> {
        const sessionId = crypto.randomUUID();
        const startTime = Date.now();

        try {
            console.log(`🎯 Starting clarity-focused voice neutralization (${config.level})`);

            // Convert buffer to audio data
            const audioData = this.bufferToFloat32Array(inputBuffer);

            if (audioData.length === 0) {
                console.log('Empty audio data, returning original');
                return inputBuffer;
            }

            // Check for voice activity to avoid processing silence
            const hasVoice = this.detectVoiceActivity(audioData);
            
            if (!hasVoice) {
                console.log('No voice activity detected, returning original audio');
                return inputBuffer;
            }

            // Apply very conservative processing to maintain clarity
            let processedData = new Float32Array(audioData);

            // Only apply F0 neutralization for basic anonymization
            if (config.processing.f0Neutralization) {
                processedData = this.applyConservativeF0Neutralization(processedData);
            }

            // Apply minimal spectral smoothing only if configured
            if (config.processing.spectralSmoothing > 0.05) {
                processedData = this.applyMinimalSpectralSmoothing(processedData, config.processing.spectralSmoothing);
            }

            // Add barely perceptible noise for masking
            if (config.processing.noiseLevel > 0.001) {
                processedData = this.addSubtleNoiseMasking(processedData, config.processing.noiseLevel);
            }

            // Final quality preservation
            processedData = this.preserveAudioQuality(processedData, audioData);

            // Convert back to buffer
            const outputBuffer = this.float32ArrayToBuffer(processedData);

            const processingTime = Date.now() - startTime;
            console.log(`✅ Clarity-focused neutralization completed in ${processingTime}ms`);

            // Log for audit
            if (userId) {
                console.log(`Voice neutralized for user ${userId}: ${config.level} level, ${processingTime}ms`);
            }

            return outputBuffer;

        } catch (error) {
            console.error('Voice neutralization failed:', error);
            // Return original audio on failure to ensure functionality
            console.log('Returning original audio due to processing error');
            return inputBuffer;
        }
    }

    /**
     * Enhanced neutralization with optional SoX integration (clarity-focused)
     */
    async neutralizeVoiceEnhanced(
        inputBuffer: Buffer,
        config: NeutralizationConfig = NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM,
        useSoxEnhancement: boolean = true,
        userId?: string
    ): Promise<Buffer> {
        const startTime = Date.now();

        try {
            console.log(`🎯 Starting enhanced voice neutralization (${config.level})`);

            // Stage 1: Apply lightweight neutralization
            let processedBuffer = await this.neutralizeVoice(inputBuffer, config, userId);

            // Stage 2: Apply SoX enhancement if available and requested
            if (useSoxEnhancement && this.soxAvailable) {
                processedBuffer = await this.applySoxNeutralizationEnhancement(processedBuffer, config);
            }

            const processingTime = Date.now() - startTime;
            console.log(`✅ Enhanced neutralization completed in ${processingTime}ms`);

            return processedBuffer;

        } catch (error) {
            console.error('Enhanced neutralization failed:', error);
            // Fallback to basic neutralization
            return this.neutralizeVoice(inputBuffer, config, userId);
        }
    }

    /**
     * Detect voice activity in audio
     */
    private detectVoiceActivity(audio: Float32Array): boolean {
        const frameSize = 1024;
        const energyThreshold = 0.001; // Very low threshold to catch quiet speech
        
        let totalEnergy = 0;
        let frameCount = 0;
        
        for (let i = 0; i < audio.length; i += frameSize) {
            const frame = audio.slice(i, Math.min(i + frameSize, audio.length));
            
            // Calculate RMS energy
            let energy = 0;
            for (let j = 0; j < frame.length; j++) {
                energy += frame[j] * frame[j];
            }
            energy = Math.sqrt(energy / frame.length);
            
            totalEnergy += energy;
            frameCount++;
        }
        
        const avgEnergy = totalEnergy / frameCount;
        return avgEnergy > energyThreshold;
    }

    /**
     * Apply conservative F0 neutralization that preserves clarity
     */
    private applyConservativeF0Neutralization(audio: Float32Array): Float32Array {
        if (audio.length < 512) {
            return audio; // Skip processing for very short segments
        }

        // Estimate current fundamental frequency using autocorrelation
        const currentF0 = this.estimateF0Autocorrelation(audio);

        if (currentF0 < 80 || currentF0 > 400) {
            return audio; // Skip if F0 detection is unreliable
        }

        // Apply very conservative F0 adjustment to preserve clarity
        const targetF0 = this.calculateConservativeNeutralF0(currentF0);
        const shiftRatio = targetF0 / currentF0;

        // Only apply minimal shift that preserves intelligibility
        if (Math.abs(shiftRatio - 1.0) > 0.05 && Math.abs(shiftRatio - 1.0) < 0.15) {
            return this.conservativePitchShift(audio, shiftRatio);
        }

        return audio;
    }

    /**
     * Fast F0 estimation using autocorrelation
     */
    private estimateF0Autocorrelation(audio: Float32Array): number {
        const minPeriod = Math.floor(this.sampleRate / 500); // 500Hz max
        const maxPeriod = Math.floor(this.sampleRate / 50);  // 50Hz min

        let maxCorrelation = 0;
        let bestPeriod = minPeriod;

        // Autocorrelation for period detection
        for (let period = minPeriod; period <= maxPeriod && period < audio.length / 2; period++) {
            let correlation = 0;
            let count = 0;

            for (let i = 0; i < audio.length - period; i++) {
                correlation += audio[i] * audio[i + period];
                count++;
            }

            correlation /= count;

            if (correlation > maxCorrelation) {
                maxCorrelation = correlation;
                bestPeriod = period;
            }
        }

        return this.sampleRate / bestPeriod;
    }

    /**
     * Calculate conservative neutral F0 for better clarity preservation
     */
    private calculateConservativeNeutralF0(currentF0: number): number {
        // Very conservative mapping to preserve speech naturalness and clarity
        if (currentF0 < 120) return currentF0 * 1.05;               // Minimal boost for low voices
        if (currentF0 < 140) return 145;                            // Slight adjustment for low-mid
        if (currentF0 < 160) return 155;                            // Minimal change for mid-range
        if (currentF0 < 180) return 170;                            // Slight adjustment for high-mid
        if (currentF0 < 200) return 185;                            // Conservative adjustment
        if (currentF0 < 240) return currentF0 * 0.95;              // Minimal reduction for high voices
        return Math.min(currentF0 * 0.98, 210);                    // Very gentle reduction for very high
    }

    /**
     * Conservative pitch shifting that preserves speech quality
     */
    private conservativePitchShift(audio: Float32Array, ratio: number): Float32Array {
        if (Math.abs(ratio - 1.0) < 0.02) return audio; // Skip minimal changes

        const output = new Float32Array(audio.length);
        const frameSize = 512; // Smaller frame for better quality
        const hopSize = frameSize / 8; // More overlap for smoother transitions

        // Use Hanning window for smooth transitions
        const window = new Float32Array(frameSize);
        for (let i = 0; i < frameSize; i++) {
            window[i] = 0.5 * (1 - Math.cos(2 * Math.PI * i / (frameSize - 1)));
        }

        for (let pos = 0; pos < audio.length - frameSize; pos += hopSize) {
            // Extract windowed frame
            const frame = new Float32Array(frameSize);
            for (let i = 0; i < frameSize; i++) {
                if (pos + i < audio.length) {
                    frame[i] = audio[pos + i] * window[i];
                }
            }

            // Apply very gentle time-domain stretching
            const shiftedFrame = this.gentleTimeStretch(frame, ratio);

            // Overlap-add with careful gain control
            const outputPos = Math.floor(pos * ratio);
            for (let i = 0; i < shiftedFrame.length && outputPos + i < output.length; i++) {
                output[outputPos + i] += shiftedFrame[i] * 0.3; // Lower gain to prevent artifacts
            }
        }

        // Light normalization to maintain levels
        return this.gentleNormalize(output);
    }

    /**
     * Very gentle time stretching for minimal artifacts
     */
    private gentleTimeStretch(frame: Float32Array, ratio: number): Float32Array {
        const outputLength = Math.floor(frame.length / ratio);
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            const sourceIndex = i * ratio;
            const lowerIndex = Math.floor(sourceIndex);
            const upperIndex = Math.min(lowerIndex + 1, frame.length - 1);
            const fraction = sourceIndex - lowerIndex;

            // Linear interpolation for smooth transitions
            output[i] = frame[lowerIndex] * (1 - fraction) + frame[upperIndex] * fraction;
        }

        return output;
    }

    /**
     * Gentle normalization that preserves dynamics
     */
    private gentleNormalize(audio: Float32Array): Float32Array {
        // Find peak amplitude
        let maxAmplitude = 0;
        for (let i = 0; i < audio.length; i++) {
            maxAmplitude = Math.max(maxAmplitude, Math.abs(audio[i]));
        }

        // Only normalize if significantly over 0.7 to preserve dynamics
        if (maxAmplitude > 0.7) {
            const normalizationFactor = 0.7 / maxAmplitude;
            for (let i = 0; i < audio.length; i++) {
                audio[i] *= normalizationFactor;
            }
        }

        return audio;
    }

    /**
     * Apply minimal spectral smoothing to preserve clarity
     */
    private applyMinimalSpectralSmoothing(audio: Float32Array, smoothingFactor: number): Float32Array {
        // Apply very light smoothing to preserve speech clarity
        const adaptiveWindow = Math.max(2, Math.floor(smoothingFactor * 8)); // Much smaller window
        const output = new Float32Array(audio.length);

        for (let i = 0; i < audio.length; i++) {
            let sum = 0;
            let weight = 0;

            const start = Math.max(0, i - adaptiveWindow);
            const end = Math.min(audio.length, i + adaptiveWindow + 1);

            for (let j = start; j < end; j++) {
                const distance = Math.abs(j - i);
                const w = Math.exp(-distance * distance / (2 * adaptiveWindow * adaptiveWindow)); // Gaussian weight
                sum += audio[j] * w;
                weight += w;
            }

            // Blend original with smoothed (preserve more original)
            const smoothed = sum / weight;
            output[i] = audio[i] * 0.85 + smoothed * 0.15; // Much more original signal preserved
        }

        return output;
    }

    /**
     * Add very subtle noise for voice masking
     */
    private addSubtleNoiseMasking(audio: Float32Array, noiseLevel: number): Float32Array {
        const output = new Float32Array(audio.length);

        for (let i = 0; i < audio.length; i++) {
            // Generate very subtle pink noise
            const noise = this.generateSubtlePinkNoise(i) * noiseLevel;
            output[i] = audio[i] + noise;

            // Gentle clipping prevention
            output[i] = Math.max(-0.95, Math.min(0.95, output[i]));
        }

        return output;
    }

    /**
     * Generate very subtle pink noise for masking
     */
    private generateSubtlePinkNoise(index: number): number {
        // Generate very gentle pink noise
        const white = (Math.random() - 0.5) * 2;
        const pink = white * Math.pow((index % 100) + 1, -0.25); // Much gentler than before
        return pink * 0.05; // Very low amplitude
    }

    /**
     * Preserve audio quality by blending with original
     */
    private preserveAudioQuality(processed: Float32Array, original: Float32Array): Float32Array {
        const enhanced = new Float32Array(processed.length);
        
        // Apply gentle enhancement to maintain speech quality
        for (let i = 0; i < processed.length; i++) {
            // Preserve significant amount of original signal for clarity
            enhanced[i] = processed[i] * 0.7 + original[i] * 0.3;
            
            // Gentle limiting to prevent distortion
            enhanced[i] = Math.max(-0.9, Math.min(0.9, enhanced[i]));
        }
        
        return this.gentleNormalize(enhanced);
    }

    /**
     * Apply SoX-based neutralization enhancements (clarity-focused)
     */
    private async applySoxNeutralizationEnhancement(
        audioBuffer: Buffer,
        config: NeutralizationConfig
    ): Promise<Buffer> {
        if (!this.soxAvailable) {
            return audioBuffer;
        }

        const inputFile = path.join(this.tempDir, `sox_input_${Date.now()}_${crypto.randomBytes(4).toString('hex')}.wav`);
        const outputFile = path.join(this.tempDir, `sox_output_${Date.now()}_${crypto.randomBytes(4).toString('hex')}.wav`);

        try {
            // Write input audio to temporary file
            await fs.writeFile(inputFile, audioBuffer);

            // Build SoX command for neutralization enhancement (very conservative)
            const soxArgs = [inputFile, outputFile];

            // Apply neutralization-specific SoX effects (very conservative)
            if (config.processing.f0Neutralization) {
                // Apply very subtle pitch adjustment for F0 neutralization
                soxArgs.push('pitch', '-0.5'); // Even more minimal pitch reduction
            }

            // Skip aggressive filtering to preserve clarity
            if (config.processing.spectralSmoothing > 0.1) {
                // Apply very gentle high-frequency smoothing only
                const cutoffFreq = Math.max(7000, 8000 - (config.processing.spectralSmoothing * 500));
                soxArgs.push('lowpass', cutoffFreq.toString());
            }

            // Minimal noise addition
            if (config.processing.noiseLevel > 0.002) {
                const noiseLevel = Math.min(0.003, config.processing.noiseLevel);
                soxArgs.push('synth', 'whitenoise', 'vol', noiseLevel.toString(), 'mix');
            }

            // Always normalize for consistent output
            soxArgs.push('norm', '-0.5');

            // Execute SoX with timeout
            await this.executeSoxWithTimeout(soxArgs, config.latencyTarget);

            // Read processed audio
            const processedAudio = await fs.readFile(outputFile);

            // Cleanup temporary files
            await Promise.all([
                fs.unlink(inputFile).catch(() => { }),
                fs.unlink(outputFile).catch(() => { })
            ]);

            return processedAudio;

        } catch (error) {
            // Cleanup on error
            await Promise.all([
                fs.unlink(inputFile).catch(() => { }),
                fs.unlink(outputFile).catch(() => { })
            ]);
            throw error;
        }
    }

    /**
     * Execute SoX with timeout for real-time processing
     */
    private async executeSoxWithTimeout(args: string[], timeoutMs: number): Promise<void> {
        return new Promise((resolve, reject) => {
            const sox = spawn('sox', args);
            const timeout = setTimeout(() => {
                sox.kill();
                reject(new Error(`SoX neutralization timeout (${timeoutMs}ms)`));
            }, timeoutMs);

            sox.on('close', (code) => {
                clearTimeout(timeout);
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`SoX neutralization failed with code ${code}`));
                }
            });

            sox.on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    /**
     * Combine processed chunks back into a single array
     */
    private combineChunks(chunks: Float32Array[]): Float32Array {
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const combined = new Float32Array(totalLength);

        let offset = 0;
        for (const chunk of chunks) {
            combined.set(chunk, offset);
            offset += chunk.length;
        }

        return combined;
    }

    /**
     * Convert audio buffer to Float32Array for processing
     * Handles both raw PCM and WAV files
     */
    private bufferToFloat32Array(buffer: Buffer): Float32Array {
        let pcmData = buffer;

        // Check if this is a WAV file and extract PCM data
        if (buffer.length > 44 && buffer.toString('ascii', 0, 4) === 'RIFF') {
            // Find the data chunk
            let offset = 12; // Skip RIFF header
            while (offset < buffer.length - 8) {
                const chunkId = buffer.toString('ascii', offset, offset + 4);
                const chunkSize = buffer.readUInt32LE(offset + 4);

                if (chunkId === 'data') {
                    pcmData = buffer.slice(offset + 8, offset + 8 + chunkSize);
                    break;
                }

                offset += 8 + chunkSize;
            }
        }

        // Convert 16-bit PCM to Float32Array
        const samples = new Float32Array(pcmData.length / 2);

        for (let i = 0; i < samples.length; i++) {
            const int16 = pcmData.readInt16LE(i * 2);
            samples[i] = int16 / 32768.0; // Convert to float [-1, 1]
        }

        return samples;
    }

    /**
     * Convert Float32Array back to audio buffer with WAV headers
     */
    private float32ArrayToBuffer(samples: Float32Array): Buffer {
        const pcmData = Buffer.alloc(samples.length * 2);

        // Convert samples to 16-bit PCM
        for (let i = 0; i < samples.length; i++) {
            const sample = Math.max(-1, Math.min(1, samples[i])); // Clamp
            const int16 = Math.floor(sample * 32767);
            pcmData.writeInt16LE(int16, i * 2);
        }

        // Create WAV file with proper headers
        return this.createWavBuffer(pcmData, 44100, 1); // 44.1kHz, mono
    }

    /**
     * Create a proper WAV file buffer with headers
     */
    private createWavBuffer(pcmData: Buffer, sampleRate: number, channels: number): Buffer {
        const bitsPerSample = 16;
        const byteRate = sampleRate * channels * bitsPerSample / 8;
        const blockAlign = channels * bitsPerSample / 8;
        const dataSize = pcmData.length;
        const fileSize = 36 + dataSize;

        const wavBuffer = Buffer.alloc(44 + dataSize);
        let offset = 0;

        // RIFF header
        wavBuffer.write('RIFF', offset); offset += 4;
        wavBuffer.writeUInt32LE(fileSize, offset); offset += 4;
        wavBuffer.write('WAVE', offset); offset += 4;

        // fmt chunk
        wavBuffer.write('fmt ', offset); offset += 4;
        wavBuffer.writeUInt32LE(16, offset); offset += 4; // chunk size
        wavBuffer.writeUInt16LE(1, offset); offset += 2; // audio format (PCM)
        wavBuffer.writeUInt16LE(channels, offset); offset += 2;
        wavBuffer.writeUInt32LE(sampleRate, offset); offset += 4;
        wavBuffer.writeUInt32LE(byteRate, offset); offset += 4;
        wavBuffer.writeUInt16LE(blockAlign, offset); offset += 2;
        wavBuffer.writeUInt16LE(bitsPerSample, offset); offset += 2;

        // data chunk
        wavBuffer.write('data', offset); offset += 4;
        wavBuffer.writeUInt32LE(dataSize, offset); offset += 4;
        pcmData.copy(wavBuffer, offset);

        return wavBuffer;
    }

    /**
     * Get available neutralization profiles
     */
    getAvailableProfiles(): Record<string, NeutralizationConfig> {
        return NEUTRALIZATION_PROFILES;
    }

    /**
     * Estimate processing time for given audio length
     */
    estimateProcessingTime(audioLengthMs: number, config: NeutralizationConfig): number {
        const baseTimePerSecond = config.realTimeMode ? 15 : 30; // ms per second of audio (reduced)
        const complexity = config.level === 'LIGHT' ? 0.3 : config.level === 'MEDIUM' ? 0.6 : 1.0;

        return (audioLengthMs / 1000) * baseTimePerSecond * complexity;
    }

    /**
     * Check if real-time processing is feasible for given audio length
     */
    canProcessRealTime(audioLengthMs: number, config: NeutralizationConfig): boolean {
        const estimatedTime = this.estimateProcessingTime(audioLengthMs, config);
        return estimatedTime <= config.latencyTarget;
    }

    /**
     * Get optimal profile for real-time use
     */
    getOptimalRealTimeProfile(maxLatencyMs: number = 50): NeutralizationConfig {
        if (maxLatencyMs <= 20) {
            return NEUTRALIZATION_PROFILES.REAL_TIME_LIGHT;
        } else if (maxLatencyMs <= 40) {
            return NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM;
        } else {
            return NEUTRALIZATION_PROFILES.OFFLINE_HEAVY;
        }
    }

    /**
     * Cleanup temporary files
     */
    private async cleanup(files: string[]): Promise<void> {
        for (const file of files) {
            try {
                await fs.unlink(file);
            } catch (error) {
                // Ignore cleanup errors
            }
        }
    }
}

export default new LightweightVoiceNeutralizer();
